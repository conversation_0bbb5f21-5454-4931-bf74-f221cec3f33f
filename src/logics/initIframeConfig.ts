/**
 * 初始化iframe配置
 * 根据URL参数设置应用的显示状态
 */

import { useAppStoreWithOut } from '@/store/modules/app';
import { getIframeConfig, listenToParentMessage, postMessageToParent } from '@/utils/iframe';

/**
 * 初始化iframe基础配置（不依赖路由器）
 * 在应用启动早期调用
 */
export function initIframeBasicConfig() {
  const iframeConfig = getIframeConfig();

  // console.log('初始化iframe基础配置:', iframeConfig);

  // 如果在iframe中，进行基础配置
  if (iframeConfig.isInIframe) {
    // console.log('检测到在iframe环境中运行');

    // 添加iframe特定的样式类
    document.body.classList.add('iframe-mode');

    // 设置iframe特定的CSS变量
    document.documentElement.style.setProperty('--iframe-mode', '1');

    // 监听父窗口的消息
    setupIframeMessageListener();
  }

  return iframeConfig;
}

/**
 * 初始化iframe完整配置（使用store直接设置）
 * 在应用启动时调用
 */
export function initIframeConfig() {
  const iframeConfig = getIframeConfig();

  try {
    const appStore = useAppStoreWithOut();

    // console.log('初始化iframe完整配置:', iframeConfig);

    // 如果设置了隐藏导航栏参数
    if (iframeConfig.hiddenNavbar) {
      // console.log('隐藏顶部导航栏');

      // 直接通过store设置header配置
      appStore.setProjectConfig({
        headerSetting: {
          show: false,
        },
      });
    }
  } catch (error) {
    console.warn('初始化iframe配置时出错:', error);
  }

  return iframeConfig;
}

/**
 * 设置iframe消息监听器
 * 处理与父窗口的通信
 */
function setupIframeMessageListener() {
  const removeListener = listenToParentMessage((event) => {
    try {
      const { type, data } = event.data;

      switch (type) {
        case 'IFRAME_RESIZE':
          // 处理iframe尺寸变化
          handleIframeResize(data);
          break;
        case 'IFRAME_THEME_CHANGE':
          // 处理主题变化
          handleThemeChange(data);
          break;
        case 'IFRAME_CONFIG_UPDATE':
          // 处理配置更新
          handleConfigUpdate(data);
          break;
        default:
          console.log('收到未知消息类型:', type, data);
      }
    } catch (error) {
      console.error('处理iframe消息时出错:', error);
    }
  });

  // 向父窗口发送准备就绪消息
  postMessageToParent({
    type: 'IFRAME_READY',
    data: {
      timestamp: Date.now(),
      url: window.location.href,
    },
  });

  // 在窗口卸载时清理监听器
  window.addEventListener('beforeunload', removeListener);
}

/**
 * 处理iframe尺寸变化
 */
function handleIframeResize(data: any) {
  console.log('处理iframe尺寸变化:', data);
  // 可以在这里添加响应式布局调整逻辑
}

/**
 * 处理主题变化
 */
function handleThemeChange(data: any) {
  console.log('处理主题变化:', data);
  // 可以在这里添加主题切换逻辑
}

/**
 * 处理配置更新
 */
function handleConfigUpdate(data: any) {
  console.log('处理配置更新:', data);
  // 可以在这里添加动态配置更新逻辑
}
