<template>
  <BasicModal
    v-bind="$attrs"
    :title="title"
    @register="registerInnerModal"
    @ok="handleSubmit"
    @cancel="resetForm"
  >
    <BasicForm @register="registerForm">
      <!-- 这里可以添加自定义的表单组件插槽 -->
    </BasicForm>
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { computed, ref } from 'vue';
  import { 
    glassUsageLogUpdate, 
    glassUsageLogAdd, 
    glassUsageLogInfo 
  } from '@/api/security/glassUsageLog';
  import { modalSchemas } from './data';

  defineOptions({ name: 'GlassUsageLogModal' });

  const emit = defineEmits(['register', 'reload']);

  const isUpdate = ref<boolean>(false);

  const title = computed<string>(() => {
    return isUpdate.value ? '编辑AR眼镜使用记录' : '新增AR眼镜使用记录';
  });

  const retData = ref<any>({});

  const [registerInnerModal, { modalLoading, closeModal }] = useModalInner(
    async (data: { record?: Recordable; update: boolean }) => {
      modalLoading(true);
      const { record, update } = data;
      isUpdate.value = update;
      if (update && record) {
        const ret = await glassUsageLogInfo(record.id);
        retData.value = ret;

        await setFieldsValue(ret);
      }
      modalLoading(false);
    },
  );

  const [registerForm, { setFieldsValue, resetForm, validate }] = useForm({
    baseColProps: {
      span: 24,
    },
    labelWidth: 120,
    name: 'glassUsageLog_modal',
    showActionButtonGroup: false,
    schemas: modalSchemas,
  });

  async function handleSubmit() {
    try {
      const values = await validate();
      modalLoading(true);

      // 计算使用时长
      if (values.loginTime && values.logoutTime) {
        const loginTime = new Date(values.loginTime);
        const logoutTime = new Date(values.logoutTime);
        const duration = Math.round((logoutTime.getTime() - loginTime.getTime()) / (1000 * 60));
        values.totalDuration = duration;
      }

      if (isUpdate.value) {
        await glassUsageLogUpdate({ ...retData.value, ...values } as any);
      } else {
        await glassUsageLogAdd(values as any);
      }

      closeModal();
      emit('reload');
    } finally {
      modalLoading(false);
    }
  }
</script>
