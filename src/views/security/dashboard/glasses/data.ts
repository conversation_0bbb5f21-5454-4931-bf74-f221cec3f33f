import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { stationOptionSelect } from '@/api/business/station';

export const columns: BasicColumn[] = [
  { 
    title: '设备编号', 
    dataIndex: 'deviceNo',
    width: 120,
  },
  { 
    title: '使用人员工号', 
    dataIndex: 'userId',
    width: 120,
  },
  { 
    title: '使用人员名称', 
    dataIndex: 'userName',
    width: 120,
  },
  {
    title: '使用时长', 
    dataIndex: 'totalDurationStr',
    width: 120,
  },
  { 
    title: '备注', 
    dataIndex: 'remark',
    width: 200,
  },
  {
    title: '登录时间', 
    dataIndex: 'loginTime',
    width: 160,
  },
  { 
    title: '退出时间', 
    dataIndex: 'logoutTime',
    width: 160,
  },
  {
    title: '心跳时间', 
    dataIndex: 'lastActiveTime',
    width: 160,
  },
];

export const formSchemas: FormSchema[] = [
  {
    label: '设备编号',
    field: 'deviceNo',
    component: 'Input',
  },
  {
    label: '使用人员工号',
    field: 'userId',
    component: 'Input',
  },
  {
    label: '使用人员名称',
    field: 'userName',
    component: 'Input',
  },
  // {
  //   label: '站点',
  //   field: 'stationId',
  //   component: 'ApiSelect',
  //   componentProps: {
  //     api: stationOptionSelect,
  //     labelField: 'stationName',
  //     valueField: 'stationId',
  //   },
  // },
  {
    label: '登录时间',
    field: 'loginTime',
    component: 'RangePicker',
  },
  {
    label: '退出时间',
    field: 'logoutTime',
    component: 'RangePicker',
  },
];

export const modalSchemas: FormSchema[] = [
  {
    label: '设备编号',
    field: 'deviceNo',
    component: 'Input',
    required: true,
  },
  {
    label: '使用人员工号',
    field: 'userId',
    component: 'Input',
    required: true,
  },
  {
    label: '使用人员名称',
    field: 'userName',
    component: 'Input',
    required: true,
  },
  {
    label: '登录时间',
    field: 'loginTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    required: true,
  },
  {
    label: '退出时间',
    field: 'logoutTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    required: true,
  },
  {
    label: '总使用时长(分钟)',
    field: 'totalDuration',
    component: 'InputNumber',
    componentProps: {
      min: 0,
    },
    required: true,
  },
  {
    label: '站点',
    field: 'stationId',
    component: 'ApiSelect',
    componentProps: {
      api: stationOptionSelect,
      labelField: 'stationName',
      valueField: 'stationId',
    },
    required: true,
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
    componentProps: {
      rows: 3,
    },
  },
];
