<template>
  <div class="checkpoint-table-container">
    <BasicTable
      @register="registerTable"
      is-can-resize-parent
      :style="{ '--vben-basic-table-form-container-padding': 0 }"
    >
      <template #toolbar>
        <Space>
          <a-button type="primary" @click="handleAdd">新增</a-button>
          <a-button
            class="<sm:hidden"
            type="primary"
            danger
            @click="multipleRemove(inspectionPointRemove)"
            :disabled="!selected"
            >删除</a-button
          >
        </Space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'isKeyPoint'">
          <Tag :color="record.isKeyPoint === 1 ? 'red' : 'default'">
            {{ record.isKeyPoint === 1 ? '重点' : '非重点' }}
          </Tag>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '查看',
                icon: IconEnum.PREVIEW,
                type: 'primary',
                ghost: true,
                onClick: handleInfo.bind(null, record),
              },
              {
                label: '修改',
                icon: IconEnum.EDIT,
                type: 'primary',
                ghost: true,
                onClick: handleEdit.bind(null, record),
              },
              {
                label: '删除',
                icon: IconEnum.DELETE,
                type: 'primary',
                danger: true,
                ghost: true,
                popConfirm: {
                  placement: 'left',
                  title: `是否确认删除?`,
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    
    <!-- 检修点相关弹窗 -->
    <InspectionPointModal @register="registerPointModal" @reload="reload" />
    <InspectionPointInfoModal @register="registerPointInfoModal" @reload="reload" />
  </div>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { Space, Tag } from 'ant-design-vue';
  import { useModal } from '@/components/Modal';
  import {
    inspectionPointList,
    inspectionPointRemove,
  } from '@/api/security/vehicleInspectionPoint';
  import InspectionPointModal from './Modal.vue';
  import InspectionPointInfoModal from './InfoModal.vue';
  import { columns, formSchemas } from './data';
  import { IconEnum } from '@/enums/appEnum';

  defineOptions({ name: 'CheckpointTable' });

  const props = defineProps({
    inspectionItemId: {
      type: [String, Number],
      default: null,
    },
  });

  const emit = defineEmits(['pointsChange']);

  const currentInspectionItemId = ref<string | number | null>(props.inspectionItemId);

  // 扩展列配置，添加重点标识列
  const extendedColumns = [
    ...columns,
    {
      title: '重点标识',
      dataIndex: 'isKeyPoint',
      key: 'isKeyPoint',
      width: 100,
    },
  ];

  const [registerTable, { reload, multipleRemove, selected, getDataSource }] = useTable({
    title: '检修点管理',
    tableSetting: {
      settingCache: false
    },
    rowSelection: {
      type: 'checkbox',
    },
    showIndexColumn: false,
    api: (params) => {
      if (!currentInspectionItemId.value) return Promise.resolve({ data: { records: [] } });
      return inspectionPointList({
        ...params,
        itemId: currentInspectionItemId.value,
      });
    },
    rowKey: 'id',
    useSearchForm: false,
    formConfig: {
      schemas: formSchemas,
      labelWidth: 120,
      name: 'inspectionPoint',
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 24,
        lg: 6,
      },
    },
    columns: extendedColumns,
    actionColumn: {
      width: 240,
      title: '操作',
      key: 'action',
      fixed: 'right',
    },
    afterFetch: (data) => {
      // 数据变化时通知父组件
      emit('pointsChange', data);
      return data;
    },
  });

  const [registerPointModal, { openModal }] = useModal();

  function handleEdit(record: Recordable) {
    openModal(true, { record, update: true, inspectionItemId: currentInspectionItemId.value });
  }

  function handleAdd() {
    if (!currentInspectionItemId.value) return;
    openModal(true, { update: false, inspectionItemId: currentInspectionItemId.value });
  }

  async function handleDelete(record: Recordable) {
    const { id: inspectionPointId } = record;
    await inspectionPointRemove([inspectionPointId]);
    await reload();
  }

  const [registerPointInfoModal, { openModal: openInfoModal }] = useModal();
  function handleInfo(record: Recordable) {
    const { id: inspectionPointId } = record;
    openInfoModal(true, inspectionPointId);
  }

  // 监听 inspectionItemId 变化
  watch(
    () => props.inspectionItemId,
    (newId) => {
      currentInspectionItemId.value = newId;
      if (newId) {
        reload();
      }
    },
    { immediate: true }
  );

  // 暴露方法给父组件
  defineExpose({
    reload,
    getDataSource,
  });
</script>

<style scoped>

:deep() .vben-basic-table .ant-table-wrapper {
  @apply p-0;
}
</style>
