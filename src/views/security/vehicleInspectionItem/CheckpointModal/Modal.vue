<template>
  <BasicModal
    v-bind="$attrs"
    :title="title"
    @register="registerInnerModal"
    @ok="handleSubmit"
    @cancel="closeModal"
  >
    <BasicForm @register="registerForm">
      <template #isAbnormalCheckpoint="{ model, field }">
        <Select v-model:value="model[field]" placeholder="请选择" :allowClear="true">
          <SelectOption label="是" value="1" />
          <SelectOption label="否" value="0" />
        </Select>
      </template>
      <template #isKeyPoint="{ model, field }">
        <Select v-model:value="model[field]" placeholder="请选择" :allowClear="true">
          <SelectOption label="是" value="1" />
          <SelectOption label="否" value="0" />
        </Select>
      </template>
      <template #orderNum="{ model, field }">
        <a-input-number v-model:value="model[field]" :min="0" />
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { computed, ref, unref } from 'vue';
  import {
    inspectionPointUpdate,
    inspectionPointAdd,
    inspectionPointInfo,
  } from '@/api/security/vehicleInspectionPoint';
  import { modalSchemas } from './data';
  import { Select } from 'ant-design-vue';

  const SelectOption = Select.Option;

  defineOptions({ name: 'InspectionPointModal' });

  const emit = defineEmits(['register', 'reload']);

  const isUpdate = ref<boolean>(false);

  const title = computed<string>(() => {
    return isUpdate.value ? '编辑检查点' : '新增检查点';
  });

  const [registerInnerModal, { modalLoading, closeModal }] = useModalInner(
    async (data: { record?: Recordable; update: boolean; inspectionItemId: number }) => {
      modalLoading(true);
      const { record, update, inspectionItemId } = data;
      isUpdate.value = update;

      // 初始化表单数据
      const initialValues = {
        itemId: inspectionItemId,
        isKeyPoint: '0',
        status: 'active',
        result: 'pending',
      };

      if (update && record) {
        const ret = await inspectionPointInfo(record.id);
        await setFieldsValue({ ...initialValues, ...ret });
      } else {
        await resetForm();
        await setFieldsValue(initialValues);
      }

      modalLoading(false);
    },
  );

  const [registerForm, { setFieldsValue, validate, resetForm }] = useForm({
    baseColProps: {
      span: 24,
    },
    labelWidth: 120,
    name: 'inspectionPoint_modal',
    showActionButtonGroup: false,
    schemas: modalSchemas,
  });

  async function handleSubmit() {
    try {
      modalLoading(true);
      const data = await validate();
      if (unref(isUpdate)) {
        await inspectionPointUpdate(data);
      } else {
        await inspectionPointAdd(data);
      }
      emit('reload');
      closeModal();
    } catch (e) {
      console.log(e);
    } finally {
      modalLoading(false);
    }
  }
</script>

<style scoped></style>
