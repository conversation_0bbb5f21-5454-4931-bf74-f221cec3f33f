<template>
  <BasicModal
    v-bind="$attrs"
    :width="800"
    :title="`步骤详情：${stepData?.workflowName || ''}`"
    :footer="null"
    @register="registerInnerModal"
  >
    <div v-if="stepData" class="workflow-detail-modal !-mt-4">
      <a-tabs v-model:activeKey="activeTabKey" @change="handleTabChange">
        <a-tab-pane
          v-for="tab in tabList"
          :key="tab.key"
          :tab="tab.label"
        >
          <div class="tab-content">
            <!-- 人员清点 -->
            <FaceRecognitionTab
              v-if="isFaceRecognitionTab(tab.key)"
              :data="faceRecognitionData"
              :loading="loading"
              @preview-image="previewImage"
            />

            <!-- 着装检查、穿戴检查、接地线 -->
            <ManagementInfoTab
              v-else-if="isManagementInfoTab(tab.key)"
              :data="managementInfoData"
              :loading="loading"
              @preview-image="previewImage"
            />

            <!-- 工具清点 -->
            <UtensilRecognitionTab
              v-else-if="isUtensilRecognitionTab(tab.key)"
              :data="utensilRecognitionData"
              :columns="getUtensilColumns(tab.key)"
              :loading="loading"
              :is-lower-utensil="isLowerUtensilTab(tab.key)"
              @preview-image="previewImage"
            />
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 图片预览 -->
    <Image.PreviewGroup
      :preview="{ visible: previewVisible, onVisibleChange: (visible) => previewVisible = visible }"
    >
      <Image
        v-for="(img, index) in previewImages"
        :key="index"
        :src="img"
        style="display: none;"
      />
    </Image.PreviewGroup>
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { Tabs as ATabs, TabPane as ATabPane, Image } from 'ant-design-vue';

  // 导入子组件
  import FaceRecognitionTab from './WorkflowDetail/components/FaceRecognitionTab.vue';
  import ManagementInfoTab from './WorkflowDetail/components/ManagementInfoTab.vue';
  import UtensilRecognitionTab from './WorkflowDetail/components/UtensilRecognitionTab.vue';

  // 导入工具函数和常量
  import {
    parseDetectionItemToTabs,
    getFirstTabKey,
    isFaceRecognitionTab,
    isManagementInfoTab,
    isUtensilRecognitionTab,
    isLowerUtensilTab
  } from './WorkflowDetail/utils/tabUtils';
  import { UPPER_UTENSIL_COLUMNS, LOWER_UTENSIL_COLUMNS } from './WorkflowDetail/constants';

  // 导入composables
  import { useWorkflowDetailData } from './WorkflowDetail/composables/useWorkflowDetailData';
  import { useImagePreview } from './WorkflowDetail/composables/useImagePreview';

  // 导入类型
  import type { WorkflowStep } from './WorkflowDetail/types';

  defineOptions({ name: 'WorkflowDetailModal' });

  const stepData = ref<WorkflowStep | null>(null);
  const activeTabKey = ref('');

  // 使用数据管理composable
  const {
    loading,
    faceRecognitionData,
    managementInfoData,
    utensilRecognitionData,
    loadTabData,
    clearAllData
  } = useWorkflowDetailData();

  // 使用图片预览composable
  const {
    previewImages,
    previewVisible,
    previewImage
  } = useImagePreview();

  // 计算动态tabs
  const tabList = computed(() => parseDetectionItemToTabs(stepData.value));

  // 获取工具清点表格列
  function getUtensilColumns(tabKey: string) {
    return isLowerUtensilTab(tabKey) ? LOWER_UTENSIL_COLUMNS : UPPER_UTENSIL_COLUMNS;
  }

  const [registerInnerModal] = useModalInner(async (step: WorkflowStep) => {
    if (!step) return;

    stepData.value = step;
    clearAllData();

    // 如果有detectionItem，设置第一个tab为活跃状态
    const firstTabKey = getFirstTabKey(step);
    if (firstTabKey) {
      activeTabKey.value = firstTabKey;
      await loadTabData(firstTabKey, step);
    }
  });

  // 切换tab时加载数据
  async function handleTabChange(key: any) {
    activeTabKey.value = key;
    if (stepData.value) {
      await loadTabData(key, stepData.value);
    }
  }
</script>

<style scoped>
.workflow-detail-modal {
  min-height: 400px;
}

.tab-content {
  /* padding: 16px 0; */
}
</style>
