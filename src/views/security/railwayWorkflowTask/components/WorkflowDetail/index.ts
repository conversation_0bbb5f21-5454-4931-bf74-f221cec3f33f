// 导出类型
export * from './types';

// 导出常量
export * from './constants';

// 导出工具函数
export * from './utils/tabUtils';

// 导出composables
export * from './composables/useWorkflowDetailData';
export * from './composables/useImagePreview';

// 导出组件
export { default as FaceRecognitionTab } from './components/FaceRecognitionTab.vue';
export { default as ManagementInfoTab } from './components/ManagementInfoTab.vue';
export { default as UtensilRecognitionTab } from './components/UtensilRecognitionTab.vue';

// 导出特定常量（便于单独使用）
export { UTENSIL_STATUS_MAP } from './constants';
