import { ref } from 'vue';

/**
 * 图片预览功能 Composable
 */
export function useImagePreview() {
  const previewImages = ref<string[]>([]);
  const previewVisible = ref(false);

  /**
   * 预览图片
   */
  function previewImage(imageUrl: string) {
    if (!imageUrl) return;
    
    previewImages.value = [imageUrl];
    previewVisible.value = true;
  }

  /**
   * 预览多张图片
   */
  function previewMultipleImages(imageUrls: string[], currentIndex = 0) {
    if (!imageUrls || imageUrls.length === 0) return;
    
    previewImages.value = imageUrls;
    previewVisible.value = true;
  }

  /**
   * 关闭预览
   */
  function closePreview() {
    previewVisible.value = false;
  }

  return {
    previewImages,
    previewVisible,
    previewImage,
    previewMultipleImages,
    closePreview
  };
}
