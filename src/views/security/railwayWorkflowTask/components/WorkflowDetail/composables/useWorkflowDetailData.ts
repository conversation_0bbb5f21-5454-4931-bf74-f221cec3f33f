import { ref } from 'vue';
import { taskFaceRecognitionList } from '@/api/security/taskFaceRecognition';
import { eventManagementList } from '@/api/security/events';
import { taskUtensilRecognitionList } from '@/api/security/taskUtensilRecognition';
import { EVENT_TYPE_MAP, MANAGEMENT_INFO_TABS, UTENSIL_TABS } from '../constants';
import type { WorkflowStep, FaceRecognitionData, ManagementInfoData, UtensilRecognitionData } from '../types';

/**
 * 工作流详情数据管理 Composable
 */
export function useWorkflowDetailData() {
  const loading = ref(false);
  
  // 数据存储
  const faceRecognitionData = ref<FaceRecognitionData[]>([]);
  const managementInfoData = ref<ManagementInfoData | null>(null);
  const utensilRecognitionData = ref<UtensilRecognitionData[]>([]);

  /**
   * 加载Tab数据
   */
  async function loadTabData(tabKey: string, stepData: WorkflowStep) {
    if (!stepData) return;
    
    loading.value = true;
    try {
      if (tabKey === 'face') {
        await loadFaceRecognitionData(stepData);
      } else if (MANAGEMENT_INFO_TABS.includes(tabKey as any)) {
        await loadManagementInfoData(tabKey, stepData);
      } else if (UTENSIL_TABS.includes(tabKey as any)) {
        await loadUtensilRecognitionData(stepData);
      }
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 加载人员清点数据
   */
  async function loadFaceRecognitionData(stepData: WorkflowStep) {
    const response = await taskFaceRecognitionList({
      taskId: stepData.taskId,
      stepId: stepData.stepId || stepData.id
    } as any);
    faceRecognitionData.value = response.rows || [];
  }

  /**
   * 加载管理信息数据
   */
  async function loadManagementInfoData(tabKey: string, stepData: WorkflowStep) {
    const response = await eventManagementList({
      taskId: stepData.taskId,
      taskType: 2,
      sceneId: stepData.stepId || stepData.id,
      eventType: EVENT_TYPE_MAP[tabKey as keyof typeof EVENT_TYPE_MAP]
    } as any);
    managementInfoData.value = response.rows?.[0] || null;
  }

  /**
   * 加载工具识别数据
   */
  async function loadUtensilRecognitionData(stepData: WorkflowStep) {
    const response = await taskUtensilRecognitionList({
      taskId: stepData.taskId
    } as any);
    utensilRecognitionData.value = response.rows || [];
  }

  /**
   * 清空所有数据
   */
  function clearAllData() {
    faceRecognitionData.value = [];
    managementInfoData.value = null;
    utensilRecognitionData.value = [];
  }

  return {
    loading,
    faceRecognitionData,
    managementInfoData,
    utensilRecognitionData,
    loadTabData,
    clearAllData
  };
}
