/**
 * 工作流步骤接口
 */
export interface WorkflowStep {
  id: string | number;
  taskId: string | number;
  workflowName: string;
  detectionItem?: string;
  stepId?: number;
}

/**
 * Tab项接口
 */
export interface TabItem {
  key: string;
  label: string;
}

/**
 * 人脸识别数据接口
 */
export interface FaceRecognitionData {
  id: number;
  taskId: number;
  taskNum?: string;
  stepId: number;
  stepName?: string;
  userNo?: string;
  userName?: string;
  status: string; // 1-不符合，2-符合
  detectImagesUrl?: string;
  detectTime?: string;
  remark?: string;
}

/**
 * 管理信息数据接口
 */
export interface ManagementInfoData {
  id?: number;
  taskId?: string;
  taskType?: string;
  sceneId?: string;
  sceneName?: string;
  responsiblePerson?: string;
  eventType?: string;
  eventLevel?: string;
  status?: string; // 1-未确认，2-误报已确认，3-异常已确认
  occurrenceTime?: string;
  remark?: string;
  annexUrl?: string;
  annexType?: string;
  stationId?: string;
  stationName?: string;
  arGlassId?: string;
  arGlassNum?: string;
}

/**
 * 工具识别数据接口
 */
export interface UtensilRecognitionData {
  id?: number;
  taskId: number;
  taskNum?: string;
  upperStepId?: number;
  upperStepName?: string;
  lowerStepId?: number;
  lowerStepName?: string;
  utensilName?: string;
  upperTrackNum?: number;
  lowerTrackNum?: number;
  upperTrackDetectImagesUrl?: string;
  upperTrackDetectTime?: string;
  lowerTrackDetectImagesUrl?: string;
  lowerTrackDetectTime?: string;
  remark?: string;
  status?: string;
}

/**
 * 表格列定义类型
 */
export interface TableColumn {
  title: string;
  dataIndex?: string;
  key: string;
  width?: number;
  align?: string
}
