import type { TableColumn } from './types';

/**
 * 标签映射关系
 */
export const TAB_LABEL_MAP = {
  face: '人员清点',
  helmet_workwear: '着装检查',
  insulating_gloves_boots: '穿戴检查',
  grounding_wire: '接地线',
  upper_utensil: '工具清点（上道）',
  lower_utensil: '工具清点（上下道）'
} as const;

/**
 * 事件类型映射关系
 */
export const EVENT_TYPE_MAP = {
  helmet_workwear: 8,
  insulating_gloves_boots: 9,
  grounding_wire: 7
} as const;

/**
 * 管理信息相关的Tab类型
 */
export const MANAGEMENT_INFO_TABS = ['helmet_workwear', 'insulating_gloves_boots', 'grounding_wire'] as const;

/**
 * 工具清点相关的Tab类型
 */
export const UTENSIL_TABS = ['upper_utensil', 'lower_utensil'] as const;

/**
 * 工具清点（上道）表格列定义
 */
export const UPPER_UTENSIL_COLUMNS: TableColumn[] = [
  { title: '工具名称', dataIndex: 'utensilName', key: 'utensilName' },
  { title: '上道数', dataIndex: 'upperTrackNum', key: 'upperTrackNum' },
  // { title: '上道时间', dataIndex: 'upperTrackDetectTime', key: 'upperTrackDetectTime' },
];

/**
 * 工具清点（上下道）表格列定义
 */
export const LOWER_UTENSIL_COLUMNS: TableColumn[] = [
  { title: '工具名称', dataIndex: 'utensilName', key: 'utensilName', align:'center' },
  { title: '上道数', dataIndex: 'upperTrackNum', key: 'upperTrackNum', align: 'center' },
  { title: '下道数', dataIndex: 'lowerTrackNum', key: 'lowerTrackNum', align: 'center' },
  // { title: '上道时间', dataIndex: 'upperTrackDetectTime', key: 'upperTrackDetectTime', align:'center' },
  // { title: '下道时间', dataIndex: 'lowerTrackDetectTime', key: 'lowerTrackDetectTime', align:'center' },
  { title: '状态', dataIndex: 'status', key: 'status', align: 'center' },
];

/**
 * 状态文本映射
 */
export const STATUS_TEXT_MAP = {
  '1': '未确认',
  '2': '误报已确认',
  '3': '异常已确认'
} as const;

/**
 * 人脸识别状态映射
 */
export const FACE_STATUS_MAP = {
  '1': { text: '不符合', icon: 'fa:times-circle', class: 'text-red-500' },
  '2': { text: '符合', icon: 'fa:check-circle', class: 'text-green-500' }
} as const;

/**
 * 工具清点状态映射
 */
export const UTENSIL_STATUS_MAP = {
  '1': '一致',
  '2': '不一致'
} as const;
