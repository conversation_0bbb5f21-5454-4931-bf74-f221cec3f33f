<template>
  <BasicModal
    v-bind="$attrs"
    :width="600"
    title="工具详情"
    :footer="null"
    @register="registerInnerModal"
  >
    <Description v-show="!showSkeleton" @register="registerDescription" />
    <Skeleton v-if="showSkeleton" active />
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { warehouseTaskUtensilListInfo } from '@/api/security/warehouse';
  import { Description, useDescription } from '@/components/Description';
  import { utensilListDescSchema } from './info';
  import { Skeleton } from 'ant-design-vue';
  import { ref } from 'vue';

  defineOptions({ name: 'UtensilInfoModal' });

  const showSkeleton = ref<boolean>(true);

  const [registerDescription, { setDescProps }] = useDescription({
    schema: utensilListDescSchema,
    column: 2,
  });

  const [registerInnerModal, { closeModal }] = useModalInner(async (utensilId: string | number) => {
    showSkeleton.value = true;
    if (!utensilId) {
      return closeModal();
    }

    try {
      const response = await warehouseTaskUtensilListInfo(utensilId);
      // 赋值
      setDescProps({ data: response });
      showSkeleton.value = false;
    } catch (error) {
      console.error('获取工具详情失败:', error);
      showSkeleton.value = false;
    }
  });
</script>

<style scoped></style>
