<template>
  <div class="p-4">
    <h1>Drawer 测试页面</h1>
    <Button type="primary" @click="openDrawer">打开检修项详情 Drawer</Button>
    
    <InspectionItemDetailDrawer @register="registerDetailDrawer" />
  </div>
</template>

<script setup lang="ts">
  import { Button } from 'ant-design-vue';
  import { useDrawer } from '@/components/Drawer';
  import InspectionItemDetailDrawer from '@/views/security/vehicleInspectionItem/DetailDrawer.vue';

  const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();

  const openDrawer = () => {
    // 使用一个测试 ID
    openDetailDrawer(true, 1);
  };
</script>
