import type { Router } from 'vue-router';
import { useUserStoreWithOut } from '@/store/modules/user';
import { getIframeConfig, getUrlParam } from '@/utils/iframe';
import { tenantList } from '@/api/auth';
import { PageEnum } from '@/enums/pageEnum';
import { notification } from 'ant-design-vue';

/**
 * 自动登录守卫
 * 检查URL参数中的loginUserId，如果存在且用户未登录，则自动执行登录
 */
export function createAutoLoginGuard(router: Router) {
  const userStore = useUserStoreWithOut();
  let autoLoginAttempted = false; // 防止重复尝试自动登录

  router.beforeEach(async (to, _from, next) => {
    // 如果已经尝试过自动登录，跳过
    if (autoLoginAttempted) {
      next();
      return;
    }

    // 获取当前token状态
    const token = userStore.getToken;
    
    // 如果已经登录，跳过自动登录
    if (token) {
      next();
      return;
    }

    // 获取iframe配置
    const iframeConfig = getIframeConfig();
    
    // 如果没有loginUserId参数，跳过自动登录
    if (!iframeConfig.loginUserId) {
      next();
      return;
    }

    // 标记已尝试自动登录，防止重复
    autoLoginAttempted = true;

    try {
      console.log('开始自动登录，用户ID:', iframeConfig.loginUserId);
      
      // 获取租户ID，优先从URL参数获取，否则使用默认值
      let tenantId = getUrlParam('tenantId') || '000000';

      if(!tenantId) {
        // 如果没有指定租户ID，尝试获取租户列表并使用第一个
        try {
          const tenantResp = await tenantList();
          if (tenantResp.tenantEnabled && tenantResp.voList.length > 0) {
            tenantId = tenantResp.voList[0].tenantId;
          }
        } catch (error) {
          console.warn('获取租户列表失败，使用默认租户ID:', error);
        }
      }

      // 构造自动登录参数
      const loginParams = {
        grantType: 'userid' as const,
        userId: iframeConfig.loginUserId,
        tenantId,
      };

      // 调用自动登录接口
      const userInfo = await userStore.login({
        ...loginParams,
        mode: 'none', // 不显示默认错误提示
      });

      if (userInfo) {
        console.log('自动登录成功:', userInfo.nickName);
        
        // 显示登录成功提示
        // notification.success({
        //   message: '自动登录成功',
        //   description: `欢迎回来，${userInfo.nickName}！`,
        //   duration: 3,
        // });

        // 如果目标路由是登录页，重定向到首页
        if (to.path === PageEnum.BASE_LOGIN) {
          next('/');
          return;
        }
      }
    } catch (error) {
      console.error('自动登录失败:', error);
      
      // 自动登录失败时的处理
      notification.error({
        message: '自动登录失败',
        description: '请手动登录',
        duration: 5,
      });
    }

    next();
  });
}
