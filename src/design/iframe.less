/**
 * iframe模式下的样式优化
 */

/* iframe模式下的基础样式 */
.iframe-mode {
  /* 移除可能的边距和内边距 */
  margin: 0;
  padding: 0;
  
  /* 确保内容填满iframe */
  height: 100vh;
  overflow: hidden;
  
  /* 禁用文本选择（可选） */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  
  /* 优化字体渲染 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* iframe模式下的布局调整 */
.iframe-mode .vben-layout {
  /* 确保布局填满容器 */
  height: 100vh;
  min-height: 100vh;
}

/* iframe模式下隐藏可能的滚动条 */
.iframe-mode .vben-layout-content {
  /* 如果需要滚动，使用自定义滚动条 */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.iframe-mode .vben-layout-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.iframe-mode .vben-layout-content::-webkit-scrollbar-track {
  background: transparent;
}

.iframe-mode .vben-layout-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.iframe-mode .vben-layout-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

/* iframe模式下的响应式调整 */
@media (max-width: 768px) {
  .iframe-mode {
    /* 移动端优化 */
    font-size: 14px;
  }
  
  .iframe-mode .ant-table {
    /* 表格在小屏幕下的优化 */
    font-size: 12px;
  }
}

/* iframe模式下禁用某些交互 */
.iframe-mode .fullscreen-item {
  /* 隐藏全屏按钮，因为在iframe中无法全屏 */
  display: none !important;
}

.iframe-mode .setting-drawer-trigger {
  /* 可选：隐藏设置按钮 */
  display: none !important;
}

/* iframe模式下的动画优化 */
.iframe-mode * {
  /* 减少动画以提高性能 */
  transition-duration: 0.2s !important;
  animation-duration: 0.2s !important;
}

/* iframe模式下的阴影优化 */
.iframe-mode .ant-card,
.iframe-mode .ant-modal {
  /* 减少阴影效果以适应iframe环境 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* iframe模式下的边框优化 */
.iframe-mode .vben-layout-header {
  /* 移除顶部边框 */
  border-bottom: none;
}

.iframe-mode .vben-layout-sider {
  /* 移除侧边栏边框 */
  border-right: 1px solid var(--border-color-light);
}

/* iframe模式下的z-index调整 */
.iframe-mode .ant-modal-mask,
.iframe-mode .ant-drawer-mask {
  /* 确保模态框在iframe中正确显示 */
  z-index: 1000;
}

.iframe-mode .ant-modal-wrap,
.iframe-mode .ant-drawer-wrapper {
  z-index: 1001;
}

/* iframe模式下的打印样式 */
@media print {
  .iframe-mode {
    /* 打印时的样式调整 */
    height: auto !important;
    overflow: visible !important;
  }
}
