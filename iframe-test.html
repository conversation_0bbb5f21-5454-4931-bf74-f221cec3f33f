<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iframe测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            margin: 0 auto;
        }
        .controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .control-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #40a9ff;
        }
        .iframe-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        iframe {
            width: 100%;
            height: 800px;
            border: none;
        }
        .message-log {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .message-item {
            padding: 8px;
            margin: 5px 0;
            background: #f0f0f0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>iframe嵌入测试页面</h1>
        
        <div class="controls">
            <h3>iframe配置</h3>
            <div class="control-group">
                <label>基础URL:</label>
                <input type="text" id="baseUrl" value="http://anchen.natapp1.cc/glassesWeb" style="width: 300px;">
            </div>
            <div class="control-group">
                <label>用户ID:</label>
                <input type="text" id="loginUserId" value="1" placeholder="输入用户ID进行自动登录">
            </div>
            <div class="control-group">
                <label>隐藏导航栏:</label>
                <select id="hiddenNavbar" value="Y">
                    <option value="">否</option>
                    <option value="Y">是</option>
                </select>
            </div>
            <div class="control-group">
                <label>租户ID:</label>
                <input type="text" id="tenantId" value="000000" placeholder="可选，默认为000000">
            </div>
            <div class="control-group">
                <button onclick="updateIframe()">更新iframe</button>
                <button onclick="sendMessage()">发送测试消息</button>
                <button onclick="clearLog()">清空日志</button>
            </div>
        </div>

        <div class="iframe-container">
            <iframe id="testIframe" src="about:blank"></iframe>
        </div>

        <div class="message-log">
            <h3>消息日志</h3>
            <div id="messageLog"></div>
        </div>
    </div>

    <script>
        // 监听来自iframe的消息
        window.addEventListener('message', function(event) {
            logMessage('收到消息', event.data, event.origin);
        });

        function updateIframe() {
            const baseUrl = document.getElementById('baseUrl').value;
            const loginUserId = document.getElementById('loginUserId').value;
            const hiddenNavbar = document.getElementById('hiddenNavbar').value;
            const tenantId = document.getElementById('tenantId').value;
            
            const params = new URLSearchParams();
            
            if (loginUserId) {
                params.append('loginUserId', loginUserId);
            }
            
            if (hiddenNavbar) {
                params.append('hiddenNavbar', hiddenNavbar);
            }
            
            if (tenantId) {
                params.append('tenantId', tenantId);
            }
            
            const url = baseUrl + (params.toString() ? '?' + params.toString() : '');
            
            document.getElementById('testIframe').src = url;
            logMessage('更新iframe URL', url);
        }

        function sendMessage() {
            const iframe = document.getElementById('testIframe');
            const message = {
                type: 'IFRAME_CONFIG_UPDATE',
                data: {
                    timestamp: Date.now(),
                    config: {
                        theme: 'light',
                        language: 'zh-CN'
                    }
                }
            };
            
            iframe.contentWindow.postMessage(message, '*');
            logMessage('发送消息', message);
        }

        function logMessage(type, data, origin = '') {
            const log = document.getElementById('messageLog');
            const item = document.createElement('div');
            item.className = 'message-item';
            item.innerHTML = `
                <strong>${new Date().toLocaleTimeString()}</strong> - ${type}
                ${origin ? `<br>来源: ${origin}` : ''}
                <br>数据: ${JSON.stringify(data, null, 2)}
            `;
            log.appendChild(item);
            log.scrollTop = log.scrollHeight;
        }

        function clearLog() {
            document.getElementById('messageLog').innerHTML = '';
        }

        // 初始化
        updateIframe();
    </script>
</body>
</html>
